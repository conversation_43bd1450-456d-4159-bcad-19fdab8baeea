/**
 * <PERSON><PERSON>t to validate that transaction amounts in the database match
 * the corresponding amounts in the accounting ledger
 */

import mongoose from "mongoose";
import { AccountingValidationService } from "../../services/accountingValidationService";
import logger from "../../external-services/loggerService";

async function validateTransactions(fromDate: string) {
  try {
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState === 0) {
      const mongoUri = process.env.MONGO_URI || "mongodb://localhost:27017/wealthyhood";
      await mongoose.connect(mongoUri);
      logger.info("Connected to MongoDB for validation", {
        module: "ValidateTransactions",
        method: "validateTransactions"
      });
    }

    logger.info("Starting accounting validation process", {
      module: "ValidateTransactions",
      method: "validateTransactions",
      data: { fromDate }
    });

    // Validate deposits and withdrawals
    const results = await AccountingValidationService.validateAllDbWithLedger(fromDate);

    // Log results
    for (const result of results) {
      logger.info(`Validation result for ${result.transactionType}`, {
        module: "ValidateTransactions",
        method: "validateTransactions",
        data: {
          transactionType: result.transactionType,
          isValid: result.isValid,
          transactionCount: result.transactionCount,
          ledgerEntryCount: result.ledgerEntryCount,
          dbTotalAmount: `€${result.dbTotalAmount.toFixed(2)}`,
          ledgerTotalAmount: `€${result.ledgerTotalAmount.toFixed(2)}`,
          difference: `€${result.difference.toFixed(2)}`
        }
      });

      if (!result.isValid) {
        logger.warn(`Validation failed for ${result.transactionType}`, {
          module: "ValidateTransactions",
          method: "validateTransactions",
          data: {
            difference: `€${result.difference.toFixed(2)}`,
            discrepancyCount: result.discrepancies?.length || 0
          }
        });

        // Log individual discrepancies
        if (result.discrepancies && result.discrepancies.length > 0) {
          logger.warn(`Found ${result.discrepancies.length} discrepancies for ${result.transactionType}`, {
            module: "ValidateTransactions",
            method: "validateTransactions",
            data: {
              discrepancies: result.discrepancies.map((d) => ({
                transactionId: d.transactionId,
                dbAmount: `€${d.dbAmount.toFixed(2)}`,
                ledgerAmount: `€${d.ledgerAmount.toFixed(2)}`,
                difference: `€${d.difference.toFixed(2)}`
              }))
            }
          });
        }
      } else {
        logger.info(`✅ Validation passed for ${result.transactionType}`, {
          module: "ValidateTransactions",
          method: "validateTransactions"
        });
      }
    }

    // Summary
    const allValid = results.every((r) => r.isValid);
    if (allValid) {
      logger.info("🎉 All validations passed! Accounting is in sync.", {
        module: "ValidateTransactions",
        method: "validateTransactions"
      });
    } else {
      logger.error("❌ Some validations failed. Please review discrepancies.", {
        module: "ValidateTransactions",
        method: "validateTransactions"
      });
    }

    return results;
  } catch (error) {
    logger.error("Error during validation process", {
      module: "ValidateTransactions",
      method: "validateTransactions",
      data: { error }
    });
    throw error;
  }
}

// Run the validation if this script is executed directly
if (require.main === module) {
  // Get fromDate from command line arguments - now required
  // Usage: npx ts-node src/scripts/accounting/validate-transactions.ts 2024-01-01
  const fromDate = process.argv[2];

  if (!fromDate) {
    logger.error("fromDate parameter is required", {
      module: "ValidateTransactions",
      method: "main",
      data: { usage: "npx ts-node src/scripts/accounting/validate-transactions.ts YYYY-MM-DD" }
    });
    console.error("Error: fromDate parameter is required");
    console.error("Usage: npx ts-node src/scripts/accounting/validate-transactions.ts YYYY-MM-DD");
    process.exit(1);
  }

  validateTransactions(fromDate)
    .then(() => {
      logger.info("Validation process completed", {
        module: "ValidateTransactions",
        method: "main"
      });
      process.exit(0);
    })
    .catch((error) => {
      logger.error("Validation process failed", {
        module: "ValidateTransactions",
        method: "main",
        data: { error }
      });
      process.exit(1);
    });
}

export { validateTransactions };
