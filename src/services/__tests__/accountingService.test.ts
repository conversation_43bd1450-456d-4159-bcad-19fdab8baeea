import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../tests/utils/db";
import {
  buildDepositCashTransaction,
  buildCreditTicket,
  buildUser,
  buildPortfolio,
  buildAssetTransaction,
  buildOrder,
  buildDividendTransaction,
  buildSavingsDividend,
  buildSavingsTopup,
  buildWithdrawalCashTransaction,
  buildReward
} from "../../tests/utils/generateModels";
import { AccountingService } from "../accountingService";
import AccountingLedgerStorageService from "../../external-services/accountingLedgerStorageService";
import {
  DepositMethodEnum,
  TransactionDocument,
  TransferWithIntermediaryStageEnum
} from "../../models/Transaction";
import { LedgerAccounts } from "../../types/accounting";
import { UserDocument } from "../../models/User";
import { PortfolioDocument } from "../../models/Portfolio";
import { ProviderEnum } from "../../configs/providersConfig";
import { InvoiceReferenceNumber } from "../../models/InvoiceReferenceNumber";
import { AccountingRecordIndex } from "../../models/AccountingRecordIndex";

/**
 * The following scenarios are covered:
 *
 * 1. Deposit (Instant Bank Transfer)
 * 2. Asset Buy
 * 3. Stock Dividend Receipt
 * 4. MMF Dividend Receipt
 * 5. Reinvest MMF Dividend (Buy)
 * 6. Stock Sell
 * 7. MMF Sell
 * 8. Withdrawal (Two-Step)
 */

describe("AccountingService", () => {
  const TODAY = "2025-06-12";
  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));

    await connectDb("AccountingServiceDepositTest");
    await createSqliteDb();
  });
  afterAll(async () => await closeDb());
  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  describe("generateAccountingEntriesOnTransactionUpdate", () => {
    /* ------------------------------------------------------------------
     * Deposit (Instant Bank Transfer)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for the three deposit stages for instant bank-transfer", async () => {
      const DEPOSIT_AMOUNT_CENTS = 11000; // £110.00 in cents
      const DEPOSIT_AMOUNT_EUROS = 110; // £110.00 in euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const creditTicket = await buildCreditTicket({
        status: "Credited",
        owner: user.id,
        portfolio: portfolio.id
      });

      const deposit = await buildDepositCashTransaction(
        {
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          consideration: { currency: "EUR", amount: DEPOSIT_AMOUNT_CENTS },
          linkedCreditTicket: creditTicket.id,
          portfolio: portfolio.id
        },
        user,
        portfolio
      );
      await deposit.populate("linkedCreditTicket");

      // Stage 1
      const oldDeposit1: TransactionDocument = deposit.toObject();
      deposit.set(
        {
          transferWithIntermediary: {
            [TransferWithIntermediaryStageEnum.ACQUISITION]: {
              incomingPayment: {
                providers: {
                  devengo: {
                    id: "in-1",
                    status: "confirmed",
                    accountId: "acc-1"
                  }
                }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit1);

      // Stage 2
      const oldDeposit2: TransactionDocument = deposit.toObject();
      const twSnapshot = deposit.transferWithIntermediary
        ? JSON.parse(JSON.stringify(deposit.transferWithIntermediary))
        : {};
      deposit.set(
        {
          transferWithIntermediary: {
            ...twSnapshot,
            [TransferWithIntermediaryStageEnum.COLLECTION]: {
              outgoingPayment: {
                providers: { devengo: { id: "out-1", status: "confirmed" } }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit2);

      // Stage 3
      const oldDeposit3: TransactionDocument = deposit.toObject();
      deposit.set(
        {
          providers: { wealthkernel: { id: "wk-txn-1", status: "Settled" } },
          status: "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(deposit, oldDeposit3);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(6);

      // Validate that accounting documents were created correctly for transactions
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0); // Deposits don't generate revenue entries

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // One for each deposit stage (movements only)
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(deposit.id);
        expect(record.sourceDocumentType).toBe("Transaction");
      });

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "debit", aa: 1 }, // id 1
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1 }, // id 2
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, side: "debit", aa: 2 }, // id 3
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, side: "credit", aa: 2 }, // id 4
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 3 }, // id 5
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, side: "credit", aa: 3 } // id 6
      ];

      const description = `${user.id}|${deposit.id}|bank transaction (deposit)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: DEPOSIT_AMOUNT_EUROS,
            reference_number: null,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Asset Buy
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an asset buy order including commission and broker fees", async () => {
      const COMMISSION_FEE_AMOUNT_CENTS = 200; // 200 cents = £2.00
      const COMMISSION_FEE_AMOUNT_EUROS = Decimal.div(COMMISSION_FEE_AMOUNT_CENTS, 100).toNumber();
      const BROKER_FEE_AMOUNT_CENTS = 100; // 100 cents = £1.00
      const BROKER_FEE_AMOUNT_EUROS = Decimal.div(BROKER_FEE_AMOUNT_CENTS, 100).toNumber();

      // Settlement amount submitted equals net (£9,800) + broker fee £100 -> 9,900
      const SETTLEMENT_AMOUNT_CENTS = 9900; // 9900 cents = £99.00

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTransaction.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: SETTLEMENT_AMOUNT_CENTS,
          amountSubmitted: SETTLEMENT_AMOUNT_CENTS,
          amount: SETTLEMENT_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: {
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          fx: { amount: 0.01, currency: "EUR" },
          realtimeExecution: { amount: COMMISSION_FEE_AMOUNT_EUROS - 0.01, currency: "EUR" }
        },
        isin: "US0378331005"
      });

      const oldOrder = order.toObject();
      order.set(
        {
          status: "Matched",
          providers: {
            wealthkernel: {
              id: "wk-order-1",
              status: "Matched",
              accountingBrokerFxFee: BROKER_FEE_AMOUNT_EUROS
            }
          },
          filledAt: new Date(TODAY)
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(10);

      // Validate that accounting documents were created correctly
      const invoiceRefs = await InvoiceReferenceNumber.find({}).sort({ createdAt: 1 });
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(order.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Order");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // movements, revenues, expenses
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(order.id);
        expect(record.sourceDocumentType).toBe("Order");
      });

      // Test virtual population of linkedOrder
      const populatedInvoiceRef = await InvoiceReferenceNumber.findOne({}).populate("linkedOrder");
      expect(populatedInvoiceRef?.linkedOrder).toBeDefined();
      expect(populatedInvoiceRef?.linkedOrder._id.toString()).toBe(order.id);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      // Expected amounts are converted to pounds inside AccountingService (divide by 100 twice)
      const assetLineAmount = Decimal.div(
        Decimal.sub(SETTLEMENT_AMOUNT_CENTS, BROKER_FEE_AMOUNT_CENTS),
        100
      ).toNumber();

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: assetLineAmount,
          aa: 1,
          reference_number: null
        },
        // Revenues (2 entries) - aa: 2, with invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: COMMISSION_FEE_AMOUNT_EUROS,
          aa: 2,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: COMMISSION_FEE_AMOUNT_EUROS,
          aa: 2,
          reference_number: "1"
        },
        // Expenses (4 entries) - aa: 3, no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        }
      ];

      const description = `${user.id}|${order.id}|US0378331005|asset buy`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Two-Step Withdrawal
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a two-stage withdrawal", async () => {
      const WITHDRAW_AMOUNT_CENTS = 11300; // £113.00 in cents
      const WITHDRAW_AMOUNT_EUROS = 113; // £113.00 in euros

      // Domestic user
      const user: UserDocument = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

      // Build initial withdrawal (Pending)
      const withdrawalTx = await buildWithdrawalCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: WITHDRAW_AMOUNT_CENTS, currency: "EUR" },
        status: "Pending",
        providers: { wealthkernel: { id: "wk-wd-1", status: "Pending" } }
      });

      /* Stage 1 – WealthKernel settles (client ledger → omnibus) */
      const old1: TransactionDocument = withdrawalTx.toObject();
      withdrawalTx.set(
        {
          providers: { wealthkernel: { id: "wk-wd-1", status: "Settled" } },
          status: "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(withdrawalTx, old1);

      /* Stage 2 – Devengo outgoing payment confirmed (omnibus → intermediary) */
      const old2: TransactionDocument = withdrawalTx.toObject();
      withdrawalTx.set(
        {
          transferWithIntermediary: {
            collection: {
              outgoingPayment: {
                providers: { devengo: { id: "out-wd-1", status: "confirmed" } }
              }
            }
          }
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnTransactionUpdate(withdrawalTx, old2);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);
      const amt = WITHDRAW_AMOUNT_EUROS;

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", aa: 1 },
        { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, side: "debit", aa: 1 },
        { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, side: "credit", aa: 2 },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 2 }
      ];

      const description = `${user.id}|${withdrawalTx.id}|bank transaction (withdrawal)`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: amt,
            reference_number: null,
            article_date: TODAY,
            description
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnTransactionInsert", () => {
    /* ------------------------------------------------------------------
     * Stock Dividend Receipt
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a stock dividend receipt", async () => {
      const DIVIDEND_AMOUNT_CENTS = 1; // 1 cent

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      // Build dividend transaction already settled
      const dividendTx = await buildDividendTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: DIVIDEND_AMOUNT_CENTS, currency: "EUR" },
        providers: { wealthkernel: { status: "Settled", id: "wk-div-1" } },
        settledAt: new Date(TODAY)
      });

      await AccountingService.generateAccountingEntriesOnTransactionInsert(dividendTx as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(2);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expectedAmount = Decimal.div(DIVIDEND_AMOUNT_CENTS, 100).toNumber(); // 1 → 0.01 euros

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit" },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit" }
      ];

      const description = `${user.id}|${dividendTx.id}|asset dividend`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: 1,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expectedAmount,
            reference_number: null,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * MMF Dividend Receipt
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an MMF dividend receipt with commission fee", async () => {
      const GROSS_DIVIDEND_CENTS = 10; // 10 cents
      const GROSS_DIVIDEND_EUROS = Decimal.div(GROSS_DIVIDEND_CENTS, 100).toNumber(); // 10 → 0.1 euros
      const COMMISSION_CENTS = 1; // 1 cent
      const COMMISSION_EUROS = Decimal.div(COMMISSION_CENTS, 100).toNumber(); // 1 → 0.01 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });

      const savingsDividendTx = await buildSavingsDividend({
        owner: user.id,
        portfolio: portfolio.id,
        originalDividendAmount: GROSS_DIVIDEND_CENTS,
        consideration: { amount: GROSS_DIVIDEND_CENTS, currency: "EUR" },
        fees: {
          commission: { amount: COMMISSION_EUROS, currency: "EUR" },
          fx: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        status: "Pending"
      });

      await AccountingService.generateAccountingEntriesOnTransactionInsert(savingsDividendTx as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", amount: GROSS_DIVIDEND_EUROS },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", amount: GROSS_DIVIDEND_EUROS },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", amount: COMMISSION_EUROS },
        { account: LedgerAccounts.MMF_DIVIDEND_FEES_WH, side: "credit", amount: COMMISSION_EUROS }
      ];

      const description = `${user.id}|${savingsDividendTx.id}|asset dividend`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: 1,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: expected[idx].amount,
            reference_number: null,
            article_date: TODAY,
            description
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnOrderUpdate", () => {
    /* ------------------------------------------------------------------
     * Reinvest MMF Dividend (Buy)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reinvest MMF dividend buy", async () => {
      const BUY_AMOUNT_CENTS = 300; // 300 cents = £3.00
      const BUY_AMOUNT_EUROS = Decimal.div(BUY_AMOUNT_CENTS, 100).toNumber(); // 3 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const savingsTopup = await buildSavingsTopup({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "Pending",
        consideration: {
          originalAmount: BUY_AMOUNT_CENTS,
          amountSubmitted: BUY_AMOUNT_CENTS,
          amount: BUY_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09" // MMF ISIN
      });

      const oldOrder = order.toObject();
      order.set(
        { status: "Matched", providers: { wealthkernel: { id: "wk-rt-1", status: "Matched" } } },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      // Validate that no invoice documents were created (no revenues)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(1); // Only movements, no revenues or expenses
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements only (4 entries) - aa: 1, no invoice reference (no commissions or broker fees)
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "credit", aa: 1, reference_number: null }
      ];

      const description = `${user.id}|${order.id}|IE00B404XK09|asset buy`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: BUY_AMOUNT_EUROS,
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Stock Sell
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for a stock sell", async () => {
      const SELL_AMOUNT_CENTS = 9700; // 9700 cents = £97.00
      const SELL_AMOUNT_EUROS = Decimal.div(SELL_AMOUNT_CENTS, 100).toNumber(); // 97 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetTx = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTx.id,
        side: "Sell",
        status: "Pending",
        consideration: { originalAmount: SELL_AMOUNT_CENTS, amount: SELL_AMOUNT_CENTS, currency: "EUR" },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "US0378331005"
      });

      const oldOrder = order.toObject();
      order.set(
        { status: "Matched", providers: { wealthkernel: { id: "wk-sell-1", status: "Matched" } } },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      // Validate that no invoice documents were created (no revenues)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(1); // Only movements, no revenues or expenses
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements only (4 entries) - aa: 1, no invoice reference (no commissions or broker fees)
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1, reference_number: null }
      ];

      const description = `${user.id}|${order.id}|US0378331005|asset sell`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: SELL_AMOUNT_EUROS,
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * MMF Sell
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for an MMF sell", async () => {
      const SELL_AMOUNT_CENTS = 1300; // 1300 cents = £13.00
      const SELL_AMOUNT_EUROS = Decimal.div(SELL_AMOUNT_CENTS, 100).toNumber(); // 13 euros

      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const portfolio = await buildPortfolio({ owner: user.id });
      const assetTx = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

      const order = await buildOrder({
        transaction: assetTx.id,
        side: "Sell",
        status: "Pending",
        consideration: { originalAmount: SELL_AMOUNT_CENTS, amount: SELL_AMOUNT_CENTS, currency: "EUR" },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        filledAt: new Date(TODAY),
        isin: "IE00B404XK09"
      });

      const oldOrder = order.toObject();
      order.set(
        { status: "Matched", providers: { wealthkernel: { id: "wk-sell-2", status: "Matched" } } },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnOrderUpdate(order, oldOrder as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      // Validate that no invoice documents were created (no revenues)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(1); // Only movements, no revenues or expenses
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(order.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Order");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements only (4 entries) - aa: 1, no invoice reference (no commissions or broker fees)
        { account: LedgerAccounts.ASSETS_PASSIVE, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.ASSETS_ACTIVE, side: "credit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit", aa: 1, reference_number: null },
        { account: LedgerAccounts.CLIENT_DOMESTIC, side: "credit", aa: 1, reference_number: null }
      ];

      const description = `${user.id}|${order.id}|IE00B404XK09|asset sell`;

      ordered.forEach((entry, idx) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: idx + 1,
            aa: expected[idx].aa,
            account_code: expected[idx].account,
            side: expected[idx].side,
            amount: SELL_AMOUNT_EUROS,
            reference_number: expected[idx].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });
  });

  describe("generateAccountingEntriesOnRewardUpdate", () => {
    /* ------------------------------------------------------------------
     * Reward Deposit Settlement
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward deposit settlement", async () => {
      const REWARD_AMOUNT_CENTS = 500_000; // £5,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 5000 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build reward with initial state (deposit not settled)
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Created" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: {} }
        }
      });

      // Simulate deposit settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(2);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, side: "debit" },
        { account: LedgerAccounts.BONUS_EXPENSE, side: "credit" }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: 1,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: REWARD_AMOUNT_EUROS,
            reference_number: null,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Reward Order Settlement
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward order settlement with commission and broker fees", async () => {
      const REWARD_AMOUNT_CENTS = 1_000_000; // £10,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 5.5; // £5.50 FX fee
      const BROKER_FEE_AMOUNT_EUROS = 2.5; // £2.50 broker fee
      const BROKER_FEE_AMOUNT_CENTS = Decimal.mul(BROKER_FEE_AMOUNT_EUROS, 100).toNumber(); // 250 cents
      const NET_AMOUNT_CENTS = REWARD_AMOUNT_CENTS - BROKER_FEE_AMOUNT_CENTS; // 999750 cents
      const NET_AMOUNT_EUROS = Decimal.div(NET_AMOUNT_CENTS, 100).toNumber(); // 9997.50 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build reward with deposit settled but order not settled
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Pending",
              accountingBrokerFxFee: BROKER_FEE_AMOUNT_EUROS
            }
          }
        }
      });

      // Simulate order settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "order.providers.wealthkernel.status": "Matched",
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});

      // Now we're generating 10 balanced entries for order settlement
      expect(entries.length).toBe(10);

      // Validate that accounting documents were created correctly for rewards
      const invoiceRefs = await InvoiceReferenceNumber.find({}).sort({ createdAt: 1 });
      expect(invoiceRefs.length).toBe(1);
      expect(invoiceRefs[0].linkedDocumentId.toString()).toBe(reward.id);
      expect(invoiceRefs[0].sourceDocumentType).toBe("Reward");
      expect(invoiceRefs[0].invoiceId).toBe(1);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(3); // movements, revenues, expenses
      accountingRecords.forEach((record) => {
        expect(record.linkedDocumentId.toString()).toBe(reward.id);
        expect(record.sourceDocumentType).toBe("Reward");
      });

      // Test virtual population of linkedReward
      const populatedInvoiceRef = await InvoiceReferenceNumber.findOne({}).populate("linkedReward");
      expect(populatedInvoiceRef?.linkedReward).toBeDefined();
      expect(populatedInvoiceRef?.linkedReward._id.toString()).toBe(reward.id);

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements (4 entries) - aa: 1, no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Revenues (2 entries) - aa: 2, with invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: FX_FEE_AMOUNT_EUROS,
          aa: 2,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: FX_FEE_AMOUNT_EUROS,
          aa: 2,
          reference_number: "1"
        },
        // Expenses (4 entries) - aa: 3, no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: null
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Reward Order Settlement (No Fees)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for reward order settlement with no fees", async () => {
      const REWARD_AMOUNT_CENTS = 250_000; // £2,500.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 2500 euros

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build reward with no fees
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Settled" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Pending",
              accountingBrokerFxFee: 0
            }
          }
        }
      });

      // Simulate order settlement
      const oldReward = reward.toObject();
      reward.set(
        {
          "order.providers.wealthkernel.status": "Matched",
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});
      expect(entries.length).toBe(4);

      // Validate that no invoice documents were created (no revenues)
      const invoiceRefs = await InvoiceReferenceNumber.find({});
      expect(invoiceRefs.length).toBe(0);

      const accountingRecords = await AccountingRecordIndex.find({}).sort({ createdAt: 1 });
      expect(accountingRecords.length).toBe(1); // Only movements, no revenues or expenses
      expect(accountingRecords[0].linkedDocumentId.toString()).toBe(reward.id);
      expect(accountingRecords[0].sourceDocumentType).toBe("Reward");

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Movements only (4 entries) - aa: 1, no invoice reference (no commissions or broker fees)
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });

    /* ------------------------------------------------------------------
     * Complete Reward Flow (Both Deposit and Order Settlement)
     * ------------------------------------------------------------------ */
    it("should write correct ledger entries for complete reward flow", async () => {
      const REWARD_AMOUNT_CENTS = 750_000; // £7,500.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 7500 euros
      const FX_FEE_AMOUNT_EUROS = 3.75; // £3.75 FX fee
      const BROKER_FEE_AMOUNT_EUROS = 1.88; // £1.88 broker fee
      const BROKER_FEE_AMOUNT_CENTS = Decimal.mul(BROKER_FEE_AMOUNT_EUROS, 100).toNumber();
      const NET_AMOUNT_CENTS = REWARD_AMOUNT_CENTS - BROKER_FEE_AMOUNT_CENTS;
      const NET_AMOUNT_EUROS = Decimal.div(NET_AMOUNT_CENTS, 100).toNumber();

      // Domestic user (GR) => client ledger 30-00-00-0000
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Build initial reward (neither deposit nor order settled)
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" },
          realtimeExecution: { amount: 0, currency: "EUR" }
        },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: { id: "wk-reward-deposit-1", status: "Created" } }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: { wealthkernel: {} }
        }
      });

      // Step 1: Simulate deposit settlement
      const oldReward1 = reward.toObject();
      reward.set(
        {
          "deposit.providers.wealthkernel.status": "Settled"
        },
        undefined,
        { merge: true }
      );
      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward1 as any);

      // Step 2: Simulate order settlement
      const oldReward2 = reward.toObject();

      reward.set(
        {
          "order.providers.wealthkernel.id": "wk-reward-order-1",
          "order.providers.wealthkernel.status": "Matched",
          "order.providers.wealthkernel.accountingBrokerFxFee": BROKER_FEE_AMOUNT_EUROS,
          status: "Settled"
        },
        undefined,
        { merge: true }
      );

      await AccountingService.generateAccountingEntriesOnRewardUpdate(reward, oldReward2 as any);

      const entries = await AccountingLedgerStorageService.queryLedgerEntries({});

      expect(entries.length).toBe(12); // 2 from deposit + 10 from order (now properly balanced)

      const ordered = [...entries].sort((a: any, b: any) => a.id - b.id);

      const expected = [
        // Deposit settlement (AA 1) - bonus expense correctly recorded here
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        {
          account: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          aa: 1,
          reference_number: null
        },
        // Order settlement - Movements (AA 2) - Main asset movement (4 entries) - no invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_ACTIVE,
          side: "debit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        {
          account: LedgerAccounts.ASSETS_PASSIVE,
          side: "credit",
          amount: NET_AMOUNT_EUROS,
          aa: 2,
          reference_number: null
        },
        // Order settlement - Revenues (AA 3) - WH Commission (2 entries) - with invoice reference
        {
          account: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: FX_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: "1"
        },
        {
          account: LedgerAccounts.COMMISSION_FEES_WH,
          side: "credit",
          amount: FX_FEE_AMOUNT_EUROS,
          aa: 3,
          reference_number: "1"
        },
        // Order settlement - Expenses (AA 4) - Broker fee (4 entries) - no invoice reference
        {
          account: LedgerAccounts.BROKER_FEE_EXPENSE,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.PAYABLES_TO_BROKER,
          side: "debit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        },
        {
          account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: BROKER_FEE_AMOUNT_EUROS,
          aa: 4,
          reference_number: null
        }
      ];

      const description = `${user.id}|${reward.id}|bank transaction (bonus)`;

      ordered.forEach((entry, index) => {
        expect(entry).toEqual(
          expect.objectContaining({
            id: index + 1,
            aa: expected[index].aa,
            account_code: expected[index].account,
            side: expected[index].side,
            amount: expected[index].amount,
            reference_number: expected[index].reference_number,
            article_date: TODAY,
            description
          })
        );
      });
    });
  });
});
