import { faker } from "@faker-js/faker";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildDailyPortfolioSavingsTicker,
  buildDepositCashTransaction,
  buildOrder,
  buildPortfolio,
  buildSavingsDividend,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildUser
} from "../../tests/utils/generateModels";
import SavingsProductService from "../savingsProductService";
import { SavingsDividendTransactionDocument, SavingsTopupTransaction } from "../../models/Transaction";
import Decimal from "decimal.js";
import { SavingsProductDocument } from "../../models/SavingsProduct";
import { DailyPortfolioSavingsTickerDocument } from "../../models/DailyTicker";
import ConfigUtil from "../../utils/configUtil";
import CurrencyUtil from "../../utils/currencyUtil";

describe("SavingsProductService", () => {
  beforeAll(async () => await connectDb("SavingsProductService"));
  afterAll(async () => await closeDb());

  describe("getSavingsProductData", () => {
    const SAVINGS_PRD_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";
    const FUND_MANAGER_FEE_PERCENTAGE = 0.1;
    const INFO_AND_QUALITY_DATA = {
      informationSection: {
        baseCurrency: "GBP",
        benchmark: "Sterling Overnight Index Average Rate (SONIA)",
        distribution: "Monthly",
        fundManager: "Blackrock",
        fundName: "ICS Sterling Liquidity Fund (Premier)",
        income: "Distributing",
        isin: "IE00B3L10356"
      },
      fundQualitySection: {
        rating: "AAA",
        creditRatings: [
          {
            label: "Moody’s rating",
            rating: "AAA-mf"
          },
          {
            label: "Fitch rating",
            rating: "AAAmmf"
          },
          {
            label: "S&P rating",
            rating: "AAAm"
          }
        ],
        ratingSubtitle:
          "This is the highest possible quality rating and means this fund has the highest level of capital security and lowest level of interest rate sensitivity.",
        risk: {
          score: 1,
          scale: 7,
          subtitle: "A risk indicator of 1/7 represents the lowest possible level of risk."
        }
      }
    };

    describe("when user has no subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID)).rejects.toThrow();
      });
    });

    describe("when user has free plan", () => {
      const PLAN_FEE_PERCENTAGE = 0.6;
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let firstSavingsDividend: SavingsDividendTransactionDocument;
      let lastMonthSavingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-02-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        [firstSavingsDividend, lastMonthSavingsDividend] = await Promise.all([
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-01-02"),
            dividendMonth: "2023-12",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-02-02"),
            dividendMonth: "2024-01",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = Decimal.div(
          (firstSavingsDividend.consideration.amount as number) +
            (lastMonthSavingsDividend.consideration.amount as number),
          100
        )
          .toDP(2)
          .toNumber();
        const earnedLastMonth = Decimal.div(lastMonthSavingsDividend.consideration.amount as number, 100)
          .toDP(2)
          .toNumber();

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });

    describe("when user has paid_low plan", () => {
      const PLAN_FEE_PERCENTAGE = 0.2;
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let firstSavingsDividend: SavingsDividendTransactionDocument;
      let lastMonthSavingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-02-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        [firstSavingsDividend, lastMonthSavingsDividend] = await Promise.all([
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-01-02"),
            dividendMonth: "2023-12",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-02-02"),
            dividendMonth: "2024-01",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = Decimal.div(
          (firstSavingsDividend.consideration.amount as number) +
            (lastMonthSavingsDividend.consideration.amount as number),
          100
        )
          .toDP(2)
          .toNumber();
        const earnedLastMonth = Decimal.div(lastMonthSavingsDividend.consideration.amount as number, 100)
          .toDP(2)
          .toNumber();

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });

    describe("when user hasn't received savings dividends", () => {
      const PLAN_FEE_PERCENTAGE = 0.6;
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-02-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = 0;
        const earnedLastMonth = 0;

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });

    describe("when user has received one dividend", () => {
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let savingsDividend: SavingsDividendTransactionDocument;
      const PLAN_FEE_PERCENTAGE = 0.6;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-02-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        savingsDividend = await buildSavingsDividend({
          owner: user.id,
          status: "Settled",
          settledAt: new Date("2024-02-02"),
          dividendMonth: "2024-01",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = Decimal.div(savingsDividend.consideration.amount as number, 100)
          .toDP(2)
          .toNumber();
        const earnedLastMonth = Decimal.div(savingsDividend.consideration.amount as number, 100)
          .toDP(2)
          .toNumber();

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });

    describe("when user has received multiple dividends", () => {
      const PLAN_FEE_PERCENTAGE = 0.6;
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let firstSavingsDividend: SavingsDividendTransactionDocument;
      let secondSavingsDividend: SavingsDividendTransactionDocument;
      let lastMonthSavingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-03-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        [firstSavingsDividend, secondSavingsDividend, lastMonthSavingsDividend] = await Promise.all([
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-01-02"),
            dividendMonth: "2023-12",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-02-02"),
            dividendMonth: "2024-01",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-03-02"),
            dividendMonth: "2024-02",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = Decimal.div(
          (firstSavingsDividend.consideration.amount as number) +
            (lastMonthSavingsDividend.consideration.amount as number) +
            (secondSavingsDividend.consideration.amount as number),
          100
        )
          .toDP(2)
          .toNumber();
        const earnedLastMonth = Decimal.div(lastMonthSavingsDividend.consideration.amount as number, 100)
          .toDP(2)
          .toNumber();

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });

    describe("when user has received multiple dividends but none in the last month", () => {
      const PLAN_FEE_PERCENTAGE = 0.6;
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let firstSavingsDividend: SavingsDividendTransactionDocument;
      let secondSavingsDividend: SavingsDividendTransactionDocument;
      let thirdSavingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        const TODAY = new Date("2024-04-05");
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        [firstSavingsDividend, secondSavingsDividend, thirdSavingsDividend] = await Promise.all([
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-01-02"),
            dividendMonth: "2023-12",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-02-02"),
            dividendMonth: "2024-01",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          }),
          buildSavingsDividend({
            owner: user.id,
            status: "Settled",
            settledAt: new Date("2024-03-02"),
            dividendMonth: "2024-02",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return the correct data", async () => {
        const data = await SavingsProductService.getSavingsProductData(user.id, SAVINGS_PRD_ID);

        const oneYield = savingsProduct.currentTicker.oneDayYield;
        const lifetimeEarnings = Decimal.div(
          (firstSavingsDividend.consideration.amount as number) +
            (thirdSavingsDividend.consideration.amount as number) +
            (secondSavingsDividend.consideration.amount as number),
          100
        )
          .toDP(2)
          .toNumber();
        const earnedLastMonth = 0;

        expect(data).toEqual({
          ...INFO_AND_QUALITY_DATA,
          highlightsSection: {
            oneDayYieldGross: {
              value: `${Decimal.add(oneYield, FUND_MANAGER_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (gross)"
            },
            oneDayYieldNet: {
              value: `${Decimal.sub(oneYield, PLAN_FEE_PERCENTAGE).toFixed(2)}% p.a.`,
              label: "1-day yield (net)"
            },
            earnedLastMonth: {
              value: `${CurrencyUtil.formatCurrency(earnedLastMonth, "GBP", "en")}`,
              label: "Earned last month"
            },
            lifetimeEarnings: {
              value: `${CurrencyUtil.formatCurrency(lifetimeEarnings, "GBP", "en")}`,
              label: "Lifetime earnings"
            }
          }
        });
      });
    });
  });

  describe("getUserSavings", () => {
    describe("when user has no subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      });
      afterAll(async () => await clearDb());

      it("should throw an error", async () => {
        await expect(SavingsProductService.getUserSavings(user.id)).rejects.toThrow();
      });
    });

    describe("when user has less than a cent unrealized interest", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-04-29");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.id,
          date: new Date("2024-04-28"),
          dailyAccrual: 0.43
        });
      });
      afterAll(async () => await clearDb());

      it("should return all the data but with undefined unrealised interest", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            unrealisedInterest: 0,
            displayUnrealisedInterest: undefined,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe.only("when user last monthly dividend consists of aggregated dividends", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let lastMonthSavingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-04-29");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio.id,
          savingsProduct: savingsProduct.id,
          date: new Date("2024-04-28"),
          dailyAccrual: 2
        });
        lastMonthSavingsDividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          savingsProduct: "mmf_dist_gbp",
          status: "Settled",
          settledAt: new Date("2024-01-02"),
          consideration: { amount: 2, currency: "GBP" },
          dividendMonth: "2024-03",
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          savingsProduct: "mmf_dist_gbp",
          status: "Cancelled",
          consideration: { amount: 1, currency: "GBP" },
          dividendMonth: "2024-02",
          cancelledBy: lastMonthSavingsDividend.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return the correct unrealised interest", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            unrealisedInterest: 0.01, // 2 (daily portfolio savings) - 1 (last month dividend (2) minus the dividend (1) that was aggregated into it)
            displayUnrealisedInterest: "+£0.01",
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when user has paid_low plan", () => {
      const PLAN_FEE_PERCENTAGE = 0.2; // 0.2%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "paid_low_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      });
      afterAll(async () => await clearDb());

      it("should return all the data but adjust netInterest to plan fee", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            displayUnrealisedInterest: undefined,
            unrealisedInterest: 0,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when the user's region doesn't have any savings products", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildPortfolio({
          owner: user.id
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });

        jest.spyOn(ConfigUtil, "getSavingsUniverse").mockReturnValue({} as any);
      });
      afterAll(async () => {
        await clearDb();
        jest.spyOn(ConfigUtil, "getSavingsUniverse").mockRestore();
      });

      it("should not return any savings", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        expect(data).toEqual([]);
      });
    });

    describe("when the user doesn't have savings holdings and portfolio savings tickers", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildPortfolio({
          owner: user.id,
          savings: new Map() // No savings holdings
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
      });
      afterAll(async () => await clearDb());

      it("should return undefined unrealisedAmount and savingsAmount equal to zero", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            displayUnrealisedInterest: undefined,
            unrealisedInterest: 0,
            savingsAmount: 0,
            displaySavingsAmount: "£0.00",
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when the user doesn't have savings holdings but some portfolio savings tickers exist", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let dailyPortfolioSavingsTickers: DailyPortfolioSavingsTickerDocument[];

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        dailyPortfolioSavingsTickers = await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-02")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-03")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-04")
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return all the data with unrealisedInterest equal to the accrual sum", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        const dailyAccrualSum = dailyPortfolioSavingsTickers.reduce(
          (sum, ticker) => sum.plus(ticker.dailyAccrual),
          new Decimal(0)
        );

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            displayUnrealisedInterest: `+${CurrencyUtil.formatCurrency(
              Decimal.div(dailyAccrualSum, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            unrealisedInterest: Decimal.div(dailyAccrualSum, 100).toDP(2).toNumber(),
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when it's the start of the month but last months dividend is not paid yet", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let dailyPortfolioSavingsTickersOfLastMonth: DailyPortfolioSavingsTickerDocument[];
      let dailyPortfolioSavingsTickers: DailyPortfolioSavingsTickerDocument[];

      beforeAll(async () => {
        const TODAY = new Date("2024-03-04");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        dailyPortfolioSavingsTickersOfLastMonth = await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-27")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-28")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-29")
          })
        ]);
        dailyPortfolioSavingsTickers = await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-01")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-02")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-03")
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return all the data with an unrealisedInterest that includes last month's accruals", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        const dailyAccrualSumOfLastMonth = dailyPortfolioSavingsTickersOfLastMonth.reduce(
          (sum, ticker) => sum.plus(ticker.dailyAccrual),
          new Decimal(0)
        );
        const dailyAccrualSum = dailyPortfolioSavingsTickers.reduce(
          (sum, ticker) => sum.plus(ticker.dailyAccrual),
          new Decimal(0)
        );
        const accrualSumIncludingLastMonth = Decimal.add(dailyAccrualSumOfLastMonth, dailyAccrualSum);

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            unrealisedInterest: Decimal.div(accrualSumIncludingLastMonth, 100).toDP(2).toNumber(),
            displayUnrealisedInterest: `+${CurrencyUtil.formatCurrency(
              Decimal.div(accrualSumIncludingLastMonth, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when it's the start of the month but last months dividend is not paid out", () => {
      const PLAN_FEE_PERCENTAGE = 0.6; // 0.6%
      const SAVINGS_AMOUNT = 1000; // 10.00 GBP
      let user: UserDocument;
      let savingsProduct: SavingsProductDocument;
      let dailyPortfolioSavingsTickersOfCurrentMonth: DailyPortfolioSavingsTickerDocument[];

      beforeAll(async () => {
        const TODAY = new Date("2024-03-04");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        savingsProduct = await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        const dailyPortfolioSavingsTickersOfLastMonth = await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-27")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-28")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-02-29")
          })
        ]);

        const dailyAccrualSumOfLastMonth = dailyPortfolioSavingsTickersOfLastMonth
          .reduce((sum, ticker) => sum.plus(ticker.dailyAccrual), new Decimal(0))
          .toNumber();
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          createdAt: new Date("2024-03-01"),
          settledAt: new Date("2024-03-01"),
          dividendMonth: "2024-02", // Dividend for last month
          consideration: {
            amount: dailyAccrualSumOfLastMonth,
            currency: "GBP"
          }
        });
        dailyPortfolioSavingsTickersOfCurrentMonth = await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-01")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-02")
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-03")
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should return all the data with an unrealisedInterest of the current month's accruals", async () => {
        const data = await SavingsProductService.getUserSavings(user.id);

        const dailyAccrualSumOfCurrentMonth = dailyPortfolioSavingsTickersOfCurrentMonth.reduce(
          (sum, ticker) => sum.plus(ticker.dailyAccrual),
          new Decimal(0)
        );

        expect(data).toEqual([
          {
            savingsProductId: "mmf_dist_gbp",
            netInterestRate: `${Decimal.sub(savingsProduct.currentTicker.oneDayYield, PLAN_FEE_PERCENTAGE).toFixed(
              2
            )}%`,
            unrealisedInterest: Decimal.div(dailyAccrualSumOfCurrentMonth, 100).toDP(2).toNumber(),
            displayUnrealisedInterest: `+${CurrencyUtil.formatCurrency(
              Decimal.div(dailyAccrualSumOfCurrentMonth, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            savingsAmount: Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
            displaySavingsAmount: `${CurrencyUtil.formatCurrency(
              Decimal.div(SAVINGS_AMOUNT, 100).toDP(2).toNumber(),
              "GBP",
              "en"
            )}`,
            currency: "GBP"
          }
        ]);
      });
    });

    describe("when the user has no savings and no savings top-up transactions", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        await buildPortfolio({
          owner: user.id,
          savings: new Map()
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        const savingsTopUps = await SavingsTopupTransaction.find({ owner: user.id });
        expect(savingsTopUps.length).toBe(0);
      });
      afterAll(async () => await clearDb());

      it("the savings amount should be 0", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(0);
      });
    });

    describe("when the user has no savings and the savings top-up is linked to an incomplete deposit", () => {
      let user: UserDocument;
      const TOP_UP_AMOUNT = 100; // cents

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map()
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        const pendingDeposit = await buildDepositCashTransaction({
          consideration: {
            amount: TOP_UP_AMOUNT,
            currency: "GBP"
          },
          status: "Pending",
          providers: {
            truelayer: {
              status: "authorizing",
              id: faker.string.uuid(),
              version: "v3"
            }
          }
        });
        await buildSavingsTopup(
          {
            status: "PendingDeposit",
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: pendingDeposit.id,
            consideration: {
              currency: "GBP",
              amount: TOP_UP_AMOUNT
            }
          },
          {
            status: "Pending",
            consideration: {
              currency: "GBP",
              amount: TOP_UP_AMOUNT
            },
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Pending"
              }
            }
          }
        );
      });
      afterAll(async () => await clearDb());

      it("the savings amount should be 0", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(0);
      });
    });

    describe("when the user has no savings and the savings top-up is linked to an executed deposit", () => {
      let user: UserDocument;
      const TOP_UP_AMOUNT = 100; // cents

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map()
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });

        const pendingDeposit = await buildDepositCashTransaction({
          consideration: {
            amount: TOP_UP_AMOUNT,
            currency: "GBP"
          },
          status: "Pending",
          providers: {
            truelayer: {
              version: "v3",
              status: "executed",
              id: faker.string.uuid()
            }
          }
        });
        await buildSavingsTopup(
          {
            status: "PendingDeposit",
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: pendingDeposit.id,
            consideration: {
              currency: "GBP",
              amount: TOP_UP_AMOUNT
            }
          },
          {},
          false
        );
      });
      afterAll(async () => await clearDb());

      it("the savings amount should not be 0", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(Decimal.div(TOP_UP_AMOUNT, 100).toNumber());
      });
    });

    describe("when the user has a internally filled savings topup and withdrawal with the same amount ", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map()
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: 100
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: []
          }
        );
        await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: 100
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: []
          }
        );
      });
      afterAll(async () => await clearDb());

      it("the savings amount should be 0", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(0);
      });
    });

    describe("when the user has a partially filled savings topup and withdrawal", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map()
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: 15000
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: [],
            consideration: {
              currency: "GBP",
              amount: 5000
            }
          }
        );
        await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: 10000
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: [],
            consideration: {
              currency: "GBP",
              amount: 5000
            }
          }
        );
      });
      afterAll(async () => await clearDb());

      it("the savings amount should be 50", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(50);
      });
    });

    describe("when the user has a savings withdrawal that is partially filled with a created order that is not yet submitted", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-02-05");
        jest.clearAllMocks();
        Date.now = jest.fn(() => +TODAY);
        user = await buildUser();
        const portfolio = await buildPortfolio({
          owner: user.id,
          savings: new Map([["mmf_dist_gbp", { amount: 5000, currency: "GBX" }]])
        });
        await buildSubscription({ owner: user.id, price: "free_monthly" });
        await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
        const savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              currency: "GBP",
              amount: 10000
            }
          },
          {},
          false
        );
        // Split transaction consideration amount into 2 orders
        const orderAmount = savingsWithdrawal.consideration.amount / 2;
        await Promise.all([
          buildOrder({
            transaction: savingsWithdrawal.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Sell",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: "GBP"
            },
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }),
          buildOrder({
            transaction: savingsWithdrawal.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Sell",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: "GBP"
            },
            status: "InternallyFilled",
            activeProviders: []
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("the savings amount should be 0", async () => {
        const savings = await SavingsProductService.getUserSavings(user.id);
        expect(savings[0].savingsAmount).toBe(0);
      });
    });
  });
});
